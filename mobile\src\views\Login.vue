<template>
  <div class="login-container">
    <div class="login-header">
      <div class="logo-container">
        <div class="logo-text">KACON-ERP移动端</div>
        <div class="logo-subtitle">企业资源管理系统</div>
      </div>
    </div>
    
    <div class="login-form">
      <Form @submit="onSubmit">
        <CellGroup inset>
          <Field
            v-model="username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
          />
          <Field
            v-model="password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="[{ required: true, message: '请输入密码' }]"
          />
        </CellGroup>
        
        <div class="submit-area">
          <Button round block type="primary" native-type="submit" :loading="loading">
            登录
          </Button>
        </div>
      </Form>
      
      <div class="version-info">
        <p>版本号：v1.0.0</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { Form, Field, CellGroup, Button, showToast } from 'vant';
import { useAuthStore } from '../stores/auth';
import { refreshFullscreenState, isIOS, isInFullscreenMode, enterIOSFullscreen, initFullscreen } from '../utils/fullscreen';
import { login as apiLogin } from '@/utils/auth';

const router = useRouter();
const authStore = useAuthStore();
const username = ref('');
const password = ref('');
const loading = ref(false);

// 处理窗口大小变化，更新自定义vh变量
const updateVhVariable = () => {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
};

// 在组件挂载时检查全屏状态
onMounted(() => {
  // 强制应用全屏模式
  document.documentElement.style.height = '100%';
  document.body.style.height = '100%';
  document.body.style.margin = '0';
  document.body.style.padding = '0';
  document.body.style.overflow = 'hidden';
  
  // 强制移除底部菜单栏
  document.querySelector('meta[name="viewport"]').setAttribute(
    'content', 
    'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
  );
  
  // 更新视口高度变量
  updateVhVariable();
  window.addEventListener('resize', updateVhVariable);
  
  // 解决iOS底部灰条问题的特别处理
  if (isIOS()) {
    // 添加特殊的iOS类
    document.body.classList.add('ios-device');
    // 在iOS上特别处理body和html样式
    document.documentElement.style.background = 'transparent';
    document.body.style.background = 'transparent';
    document.documentElement.style.webkitTapHighlightColor = 'transparent';
  }
  
  refreshFullscreenState();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateVhVariable);
});

// 专门用于在iOS设备上处理登录成功
const handleIOSLoginSuccess = () => {
  // 登录成功特殊处理
  localStorage.setItem('isLoggedIn', 'true');
  localStorage.setItem('forceFullscreen', 'true');
  
  // 应用增强的iOS全屏
  initFullscreen();
  
  // 应用标准全屏模式
  enterIOSFullscreen();
  
  // 延迟跳转并确保显示成功信息
  setTimeout(() => {
    showToast({
      type: 'success',
      message: '登录成功',
      onClose: () => {
        // 再次确保全屏模式
        initFullscreen();
        enterIOSFullscreen();
        
        // 延迟跳转，确保全屏模式有效
        setTimeout(() => {
          router.push('/');
          
          // 跳转后再次应用全屏
          setTimeout(() => {
            initFullscreen();
            enterIOSFullscreen();
          }, 100);
        }, 100);
      }
    });
  }, 300);
};

const onSubmit = async () => {
  if (!username.value || !password.value) {
    showToast('请输入用户名和密码');
    return;
  }

  loading.value = true;

  try {
    // 使用新的认证管理器
    const result = await apiLogin(username.value, password.value);

    // 同时更新旧的auth store以保持兼容性
    await authStore.login({
      username: username.value,
      password: password.value
    });

    // 在iOS上使用特殊的全屏模式处理
    if (isIOS()) {
      handleIOSLoginSuccess();
    } else {
      // 非iOS设备正常刷新全屏状态
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('forceFullscreen', 'true');
      refreshFullscreenState();

      setTimeout(() => {
        showToast({
          type: 'success',
          message: '登录成功',
          onClose: () => {
            router.push('/');
          }
        });
      }, 300);
    }
  } catch (error) {
    console.error('登录过程中发生错误:', error);
    showToast({
      type: 'fail',
      message: error.message || '登录失败，请检查用户名和密码',
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
/* 使用视口高度变量 */
:root {
  --vh: 1vh;
}

.login-container {
  min-height: 100vh; /* 回退 */
  min-height: calc(var(--vh, 1vh) * 100); /* 使用自定义变量 */
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, #1989fa, #39a9ed);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  /* 适配安全区域 */
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  box-sizing: border-box;
}

.login-header {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10vh;
  padding-top: calc(var(--vh, 1vh) * 10);
}

.logo-container {
  text-align: center;
}

.logo-text {
  color: white;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.logo-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.login-form {
  flex: 2;
  background: white;
  border-radius: 24px 24px 0 0;
  padding: 32px 20px 32px;
  position: relative;
  overflow: hidden;
  /* 确保覆盖底部 */
  margin-bottom: -50px; /* 覆盖可能存在的灰色底部条 */
  padding-bottom: calc(70px + env(safe-area-inset-bottom)); /* 为版本号提供额外空间，并考虑安全区域 */
  box-sizing: border-box;
  
  /* 解决iOS上的底部安全区域显示问题 */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(50px + env(safe-area-inset-bottom));
    background: white;
    z-index: 5;
  }
}

.submit-area {
  margin-top: 24px;
}

.version-info {
  position: absolute;
  bottom: calc(16px + env(safe-area-inset-bottom)); /* 考虑安全区域 */
  left: 0;
  right: 0;
  text-align: center;
  color: #999;
  font-size: 12px;
  z-index: 10; /* 确保显示在顶层 */
}

:deep(.van-field__label) {
  width: 4.5em;
}

/* 覆盖可能影响的样式 */
:deep(.van-button) {
  background-color: #1989fa !important;
}

/* iOS特殊处理 */
:global(.ios-device) {
  .login-form {
    border-radius: 24px 24px 0 0 !important;
    &::after {
      content: '';
      display: block;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 50px;
      background-color: white;
      z-index: 5;
    }
  }
}

/* 通用样式覆盖 */
:deep(html), :deep(body) {
  background: transparent !important;
  overflow: hidden !important;
}
</style> 