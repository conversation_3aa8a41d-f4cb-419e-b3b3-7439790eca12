<template>
  <div class="page-container">
    <NavBar title="用户调试信息" left-arrow @click-left="$router.back()" />
    
    <div class="content-container">
      <div class="debug-section">
        <h3>认证状态</h3>
        <p>是否已认证: {{ authStore.isAuthenticated }}</p>
        <p>Token: {{ authStore.token ? '已设置' : '未设置' }}</p>
      </div>
      
      <div class="debug-section">
        <h3>用户信息</h3>
        <pre>{{ JSON.stringify(authStore.user, null, 2) }}</pre>
      </div>
      
      <div class="debug-section">
        <h3>LocalStorage</h3>
        <p>Token: {{ localStorage.getItem('token') ? '已设置' : '未设置' }}</p>
        <p>User: {{ localStorage.getItem('user') ? '已设置' : '未设置' }}</p>
        <pre v-if="localStorage.getItem('user')">{{ localStorage.getItem('user') }}</pre>
      </div>
      
      <div class="debug-section">
        <h3>操作</h3>
        <Button @click="fetchProfile" type="primary" block>获取用户资料</Button>
        <Button @click="clearStorage" type="danger" block style="margin-top: 10px;">清除存储</Button>
        <Button @click="testLogin" type="default" block style="margin-top: 10px;">测试登录</Button>
      </div>
      
      <div class="debug-section">
        <h3>日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { NavBar, Button, showToast } from 'vant';
import { useAuthStore } from '../stores/auth';

const authStore = useAuthStore();
const logs = ref([]);

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > 20) {
    logs.value.pop();
  }
};

const fetchProfile = async () => {
  try {
    addLog('开始获取用户资料...');
    const result = await authStore.fetchUserProfile();
    addLog(`获取用户资料结果: ${result}`);
    addLog(`用户信息: ${JSON.stringify(authStore.user)}`);
  } catch (error) {
    addLog(`获取用户资料失败: ${error.message}`);
  }
};

const clearStorage = () => {
  localStorage.clear();
  authStore.logout();
  addLog('已清除所有存储');
  showToast('存储已清除');
};

const testLogin = async () => {
  try {
    addLog('开始测试登录...');
    const result = await authStore.login({
      username: 'admin',
      password: '123456'
    });
    addLog(`登录结果: ${result}`);
    if (result) {
      addLog('登录成功，用户信息已更新');
    }
  } catch (error) {
    addLog(`登录失败: ${error.message}`);
  }
};

onMounted(() => {
  addLog('页面已加载');
  addLog(`当前认证状态: ${authStore.isAuthenticated}`);
  addLog(`当前用户: ${JSON.stringify(authStore.user)}`);
});
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $background-color;
}

.content-container {
  padding-top: 46px;
  padding: 46px 16px 16px;
}

.debug-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  h3 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 16px;
  }
  
  p {
    margin: 8px 0;
    color: #666;
  }
  
  pre {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-family: monospace;
}
</style>
