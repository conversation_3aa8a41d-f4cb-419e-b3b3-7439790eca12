<template>
  <div class="page-container">
    <NavBar title="KACON-MES移动端" :fixed="true">
      <template #right>
        <Badge :content="5" :show-zero="false">
          <Icon name="bell" size="18" @click="handleNotification" />
        </Badge>
      </template>
    </NavBar>

    <div class="content-container">
      <!-- 调试信息面板 -->
      <div style="background: rgba(0,0,0,0.8); color: white; padding: 10px; margin: 10px; border-radius: 5px; font-size: 12px;">
        <div>认证状态: {{ authStore.isAuthenticated ? '已登录' : '未登录' }}</div>
        <div>用户数据: {{ authStore.user ? '已加载' : '未加载' }}</div>
        <div>用户名: {{ userName }}</div>
        <div>头像数据: {{ userAvatar ? `${userAvatar.length}字符` : '无' }}</div>
        <div v-if="userAvatar">头像前缀: {{ userAvatar.substring(0, 30) }}...</div>
        <div style="margin-top: 10px;">
          <button @click="testLogin" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-right: 5px;">测试登录</button>
          <button @click="initUserProfile" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px;">刷新资料</button>
        </div>
      </div>

      <!-- 用户信息和快捷操作 -->
      <div class="user-header">
        <div class="user-info">
          <div class="avatar" @click="navigateTo('/profile')" style="position: relative;">
            <img
              v-if="userAvatar"
              :src="userAvatar"
              alt="用户头像"
              class="avatar-img"
              @error="handleAvatarError"
              @load="handleAvatarLoad"
            />
            <Icon v-else name="user-o" size="24" color="#fff" />
          </div>
          <div class="user-details">
            <div class="user-name">{{ userName }}</div>
            <div class="user-role">{{ userRole }}</div>
          </div>
        </div>
        <div class="header-actions">
          <div class="action-item" @click="initUserProfile" style="margin-right: 8px;" title="刷新用户信息">
            <Icon name="replay" size="20" color="#fff" />
          </div>
          <div class="action-item" @click="handleScan">
            <Icon name="scan" size="20" color="#fff" />
          </div>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-container">
        <Search
          v-model="searchValue"
          placeholder="搜索功能、物料、订单..."
          shape="round"
          @click="handleSearch"
          readonly
        />
      </div>

      <!-- 顶部导航标签页 -->
      <Tabs v-model:active="activeTab" animated swipeable sticky>
        <Tab title="常用功能">
          <!-- 快捷统计卡片 -->
          <div class="stats-cards">
            <div class="stats-card" @click="navigateTo('/inventory')">
              <div class="stats-icon">
                <Icon name="goods-collect-o" size="24" color="#5E7BF6" />
              </div>
              <div class="stats-info">
                <div class="stats-value">1250</div>
                <div class="stats-label">库存物料</div>
              </div>
            </div>
            <div class="stats-card" @click="navigateTo('/production/tasks')">
              <div class="stats-icon">
                <Icon name="todo-list-o" size="24" color="#FF6B6B" />
              </div>
              <div class="stats-info">
                <div class="stats-value">8</div>
                <div class="stats-label">处理任务</div>
              </div>
            </div>
            <div class="stats-card" @click="navigateTo('/sales/orders')">
              <div class="stats-icon">
                <Icon name="notes-o" size="24" color="#2CCFB0" />
              </div>
              <div class="stats-info">
                <div class="stats-value">12</div>
                <div class="stats-label">处理订单</div>
              </div>
            </div>
          </div>

          <!-- 顶部滑动菜单 -->
          <div class="quick-menu">
            <Swipe :autoplay="0" :show-indicators="false">
              <SwipeItem>
                <div class="menu-row">
                  <div class="menu-item" v-for="(item, index) in topMenus.slice(0, 4)" :key="index" @click="navigateTo(item.path)">
                    <div class="menu-icon">
                      <Icon :name="item.icon" :color="item.color" size="24" />
                    </div>
                    <span class="menu-text">{{ item.title }}</span>
                  </div>
                </div>
              </SwipeItem>
              <SwipeItem>
                <div class="menu-row">
                  <div class="menu-item" v-for="(item, index) in topMenus.slice(4, 8)" :key="index" @click="navigateTo(item.path)">
                    <div class="menu-icon">
                      <Icon :name="item.icon" :color="item.color" size="24" />
                    </div>
                    <span class="menu-text">{{ item.title }}</span>
                  </div>
                </div>
              </SwipeItem>
            </Swipe>
          </div>

          <!-- 常用功能模块 -->
          <div class="module-section">
            <div class="section-header">
              <div class="module-title">常用功能</div>
              <div class="more-link" @click="activeTab = 1">查看全部</div>
            </div>
            <div class="module-grid">
              <div class="module-item" v-for="(item, index) in commonFunctions" :key="index" @click="navigateTo(item.path)">
                <div class="module-icon">
                  <Icon :name="item.icon" :color="item.color" size="28" />
                </div>
                <span class="module-item-text">{{ item.title }}</span>
                <div class="module-badge" v-if="item.badge">
                  <Badge :content="item.badge" />
                </div>
              </div>
            </div>
          </div>
        </Tab>

        <Tab title="全部应用">
          <div class="all-apps-container">
            <div v-for="(category, index) in appCategories" :key="index" class="category-section">
              <h3 class="category-title">{{ category.title }}</h3>
              <div class="apps-grid">
                <div
                  v-for="(app, appIndex) in category.apps"
                  :key="appIndex"
                  class="app-item"
                  @click="navigateTo(app.path)"
                >
                  <div class="app-icon">
                    <Icon :name="app.icon" :color="app.color" size="28" />
                  </div>
                  <span class="app-name">{{ app.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </Tab>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { NavBar, Icon, Tabs, Tab, Search, Badge, Swipe, SwipeItem, showToast } from 'vant';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const searchValue = ref('');
const activeTab = ref(0);

// 计算用户信息
const userInfo = computed(() => {
  return authStore.user || {};
});

const userName = computed(() => {
  return userInfo.value.name || userInfo.value.real_name || userInfo.value.username || '用户';
});

const userRole = computed(() => {
  if (userInfo.value.role === 'admin') return '管理员';
  if (userInfo.value.role === 'manager') return '经理';
  if (userInfo.value.role === 'user') return '用户';
  return userInfo.value.role || '用户';
});

const userAvatar = computed(() => {
  const avatar = userInfo.value.avatar;
  console.log('用户头像数据:', avatar ? '已设置' : '未设置');
  if (avatar && !avatar.startsWith('data:image/')) {
    console.warn('头像数据格式可能不正确:', avatar.substring(0, 50) + '...');
  }
  return avatar || null;
});

// 顶部滑动菜单
const topMenus = ref([
  { title: '生产管理', icon: 'todo-list-o', color: '#5E7BF6', path: '/production' },
  { title: '销售管理', icon: 'notes-o', color: '#FF6B6B', path: '/sales' },
  { title: '采购管理', icon: 'shopping-cart-o', color: '#FF9F45', path: '/purchase' },
  { title: '库存管理', icon: 'goods-collect-o', color: '#2CCFB0', path: '/inventory' },
  { title: '基础数据', icon: 'records', color: '#A48BE0', path: '/baseData' },
  { title: '财务管理', icon: 'balance-o', color: '#FFC759', path: '/finance' },
  { title: '质量管理', icon: 'certificate', color: '#FF8A80', path: '/quality' },
  { title: '系统管理', icon: 'setting-o', color: '#81C784', path: '/system' },
]);

// 常用功能
const commonFunctions = ref([
  { title: '扫码功能', icon: 'scan', color: '#5E7BF6', path: '/scan' },
  { title: '库存查询', icon: 'search', color: '#FF6B6B', path: '/inventory/stock' },
  { title: '库存盘点', icon: 'balance-list-o', color: '#FF9F45', path: '/inventory/check' },
  { title: '生产任务', icon: 'todo-list-o', color: '#2CCFB0', path: '/production/tasks', badge: 3 },
  { title: '销售订单', icon: 'notes-o', color: '#A48BE0', path: '/sales/orders', badge: 5 },
  { title: '采购订单', icon: 'shopping-cart-o', color: '#FFC759', path: '/purchase/orders' },
  { title: '物料管理', icon: 'goods-collect', color: '#FF8A80', path: '/baseData/materials' },
  { title: '客户管理', icon: 'friends-o', color: '#81C784', path: '/baseData/customers' },
]);

// 全部应用分类
const appCategories = ref([
  {
    title: '生产管理',
    apps: [
      { title: '生产计划', icon: 'calendar-o', color: '#5E7BF6', path: '/production/plan' },
      { title: '生产任务', icon: 'todo-list-o', color: '#FF6B6B', path: '/production/tasks' },
      { title: '生产过程', icon: 'setting-o', color: '#2CCFB0', path: '/production/process' },
      { title: '生产报工', icon: 'description', color: '#FF9F45', path: '/production/report' },
    ]
  },
  {
    title: '库存管理',
    apps: [
      { title: '库存查询', icon: 'search', color: '#5E7BF6', path: '/inventory/stock' },
      { title: '入库管理', icon: 'back-top', color: '#2CCFB0', path: '/inventory/inbound' },
      { title: '出库管理', icon: 'down', color: '#FF6B6B', path: '/inventory/outbound' },
      { title: '库存调拨', icon: 'exchange', color: '#FF9F45', path: '/inventory/transfer' },
      { title: '库存盘点', icon: 'checked', color: '#FFC759', path: '/inventory/check' },
      { title: '库存报表', icon: 'chart-trending-o', color: '#A48BE0', path: '/inventory/report' },
      { title: '流水报表', icon: 'column', color: '#5E7BF6', path: '/inventory/transaction' },
    ]
  },
  {
    title: '基础数据',
    apps: [
      { title: '物料管理', icon: 'bag-o', color: '#5E7BF6', path: '/baseData/materials' },
      { title: 'BOM管理', icon: 'cluster-o', color: '#FF6B6B', path: '/baseData/boms' },
      { title: '客户管理', icon: 'friends-o', color: '#2CCFB0', path: '/baseData/customers' },
      { title: '供应商管理', icon: 'shop-o', color: '#FF9F45', path: '/baseData/suppliers' },
      { title: '分类管理', icon: 'apps-o', color: '#A48BE0', path: '/baseData/categories' },
      { title: '单位管理', icon: 'balance-o', color: '#FF9F45', path: '/baseData/units' },
      { title: '库位管理', icon: 'location-o', color: '#2CCFB0', path: '/baseData/locations' },
      { title: '工序模板', icon: 'enlarge', color: '#5E7BF6', path: '/baseData/process-templates' },
    ]
  },
  {
    title: '采购管理',
    apps: [
      { title: '采购概览', icon: 'chart-trending-o', color: '#5E7BF6', path: '/purchase/dashboard' },
      { title: '采购申请', icon: 'records', color: '#FF6B6B', path: '/purchase/requisitions' },
      { title: '采购订单', icon: 'cart-o', color: '#2CCFB0', path: '/purchase/orders' },
      { title: '采购入库', icon: 'logistics', color: '#FF9F45', path: '/purchase/receipts' },
      { title: '采购退货', icon: 'revoke', color: '#A48BE0', path: '/purchase/returns' },
      { title: '外委加工', icon: 'gift-o', color: '#FF9F45', path: '/purchase/processing' },
      { title: '外委入库', icon: 'completed', color: '#2CCFB0', path: '/purchase/processing-receipts' },
      { title: 'AI采购助手', icon: 'guide-o', color: '#5E7BF6', path: '/purchase/ai-assistant' },
    ]
  },
  {
    title: '销售管理',
    apps: [
      { title: '销售订单', icon: 'notes-o', color: '#5E7BF6', path: '/sales/orders' },
      { title: '销售出库', icon: 'send-gift-o', color: '#FF6B6B', path: '/sales/outbound' },
      { title: '销售退货', icon: 'revoke', color: '#2CCFB0', path: '/sales/returns' },
      { title: '销售换货', icon: 'exchange', color: '#FF9F45', path: '/sales/exchanges' },
      { title: '销售报表', icon: 'chart-trending-o', color: '#A48BE0', path: '/sales/report' },
      { title: '客户管理', icon: 'friends-o', color: '#FFC759', path: '/sales/customers' },
      { title: '价格管理', icon: 'gold-coin-o', color: '#FF8A80', path: '/sales/pricing' },
      { title: 'AI销售助手', icon: 'guide-o', color: '#5E7BF6', path: '/sales/ai-assistant' },
    ]
  },
]);

// 处理搜索
const handleSearch = () => {
  router.push('/search');
};

// 处理扫码
const handleScan = () => {
  router.push('/scan');
};

// 处理通知
const handleNotification = () => {
  router.push('/notifications');
};

// 导航到指定页面
const navigateTo = (path) => {
  if (!path) {
    showToast('功能正在开发中');
    return;
  }
  router.push(path);
};

// 获取统计数据
const getStatistics = async () => {
  try {
    // 这里可以调用API获取实际统计数据
    // const response = await api.getHomeStatistics();
    // statistics.value = response.data;
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 头像加载处理
const handleAvatarLoad = () => {
  console.log('✅ 头像加载成功');
};

const handleAvatarError = (event) => {
  console.error('❌ 头像加载失败:', event);
  console.log('头像URL长度:', userAvatar.value?.length);
  console.log('头像URL前缀:', userAvatar.value?.substring(0, 50));

  // 测试头像数据是否有效
  if (userAvatar.value) {
    const img = new Image();
    img.onload = () => console.log('✅ 头像数据本身是有效的');
    img.onerror = () => console.log('❌ 头像数据无效');
    img.src = userAvatar.value;
  }
};

// 测试登录
const testLogin = async () => {
  try {
    console.log('🔐 开始测试登录...');
    showToast('正在登录...');

    const success = await authStore.login({
      username: 'admin',
      password: '123456'
    });

    if (success) {
      console.log('✅ 登录成功');
      showToast('登录成功');
      await initUserProfile();
    } else {
      console.log('❌ 登录失败');
      showToast('登录失败');
    }
  } catch (error) {
    console.error('❌ 登录错误:', error);
    showToast('登录错误: ' + error.message);
  }
};

// 初始化用户信息
const initUserProfile = async () => {
  try {
    console.log('🔍 当前用户信息:', authStore.user);

    // 检查是否已登录
    if (!authStore.isAuthenticated) {
      console.log('❌ 用户未登录');
      showToast('请先登录');
      return;
    }

    // 强制重新获取用户信息以确保数据最新
    console.log('🔄 正在重新获取用户信息...');
    showToast('正在获取用户信息...');
    const success = await authStore.fetchUserProfile();

    if (success) {
      console.log('✅ 获取用户信息成功:', authStore.user);
      showToast('用户信息已更新');

      // 特别检查头像数据
      if (authStore.user?.avatar) {
        console.log('🖼️ 头像数据存在，长度:', authStore.user.avatar.length);
        console.log('🖼️ 头像前缀:', authStore.user.avatar.substring(0, 50));

        // 测试头像是否能正常加载
        const testImg = new Image();
        testImg.onload = () => console.log('✅ 头像测试加载成功');
        testImg.onerror = () => console.log('❌ 头像测试加载失败');
        testImg.src = authStore.user.avatar;
      } else {
        console.log('❌ 没有头像数据');
      }
    } else {
      console.log('❌ 获取用户信息失败');
      showToast('获取用户信息失败');
    }
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error);
    showToast('获取用户信息失败');
  }
};

onMounted(() => {
  initUserProfile();
  getStatistics();
});

</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $background-color;
}

.content-container {
  padding-top: 46px; // 为固定的NavBar留出空间
}

// 用户头部
.user-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 24px;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      cursor: pointer;
      overflow: hidden;

      .avatar-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 24px;
      }
    }

    .user-details {
      .user-name {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 2px;
      }

      .user-role {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 16px;

    .action-item {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.search-container {
  padding: 12px 16px;
  background-color: #fff;
}

.stats-cards {
  display: flex;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;

  .stats-card {
    flex: 1;
    background-color: #fff;
    border-radius: 12px;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .stats-icon {
      margin-right: 12px;
    }

    .stats-info {
      .stats-value {
        font-size: 20px;
        font-weight: bold;
        color: $text-color;
        margin-bottom: 2px;
      }

      .stats-label {
        font-size: 12px;
        color: $text-color-secondary;
      }
    }
  }
}

.quick-menu {
  background-color: #fff;
  margin-bottom: 8px;

  .menu-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 16px;
    gap: 8px;
  }

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;

    .menu-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background-color: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
    }

    .menu-text {
      font-size: 12px;
      color: $text-color;
      text-align: center;
    }
  }
}

.module-section {
  margin: 16px 0;
  background-color: #fff;
  border-radius: 12px;
  margin: 8px 16px 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid $border-color;

    .module-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
    }

    .more-link {
      font-size: 14px;
      color: $primary-color;
    }
  }
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding: 16px 8px;
  gap: 8px;
}

.module-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;

  &:active {
    background-color: #f8f9fa;
  }

  .module-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }

  .module-item-text {
    font-size: 12px;
    color: $text-color;
    text-align: center;
    line-height: 1.2;
  }

  .module-badge {
    position: absolute;
    top: 8px;
    right: 8px;
  }
}

.all-apps-container {
  padding: 12px 0;

  .category-section {
    margin-bottom: 24px;
    padding: 0 16px;

    .category-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 12px;
      padding-left: 8px;
    }

    .apps-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .app-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 8px;
        border-radius: 8px;
        transition: background-color 0.2s;

        &:active {
          background-color: #f8f9fa;
        }

        .app-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background-color: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
        }

        .app-name {
          font-size: 12px;
          color: $text-color;
          text-align: center;
          line-height: 1.2;
        }
      }
    }
  }
}

.content-container {
  padding-bottom: 50px; // 为底部Tabbar预留空间
}

:deep(.van-field__left-icon) {
  margin-right: 6px;
}

:deep(.van-tabs__line) {
  background-color: $primary-color;
}
</style>