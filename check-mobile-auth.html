<!DOCTYPE html>
<html>
<head>
    <title>检查移动端认证状态</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>移动端认证状态检查</h1>
    
    <div class="section">
        <h3>LocalStorage 状态</h3>
        <div id="localStorage-status"></div>
    </div>
    
    <div class="section">
        <h3>操作</h3>
        <button onclick="checkAuth()">检查认证状态</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="clearStorage()">清除存储</button>
    </div>
    
    <div class="section">
        <h3>日志</h3>
        <div id="logs"></div>
    </div>

    <script>
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            logDiv.innerHTML = `<div>[${timestamp}] ${message}</div>` + logDiv.innerHTML;
        }

        function checkAuth() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            const statusDiv = document.getElementById('localStorage-status');
            statusDiv.innerHTML = `
                <p><strong>Token:</strong> ${token ? '已设置' : '未设置'}</p>
                <p><strong>User:</strong> ${user ? '已设置' : '未设置'}</p>
                ${user ? `<pre>${user}</pre>` : ''}
            `;
            
            addLog('已检查认证状态');
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    addLog(`用户名: ${userData.username || '未知'}`);
                    addLog(`姓名: ${userData.name || '未知'}`);
                    addLog(`头像: ${userData.avatar ? '已设置' : '未设置'}`);
                } catch (e) {
                    addLog('解析用户数据失败: ' + e.message);
                }
            }
        }

        async function testLogin() {
            try {
                addLog('开始测试登录...');
                
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                addLog('登录成功');
                
                // 保存到localStorage
                localStorage.setItem('token', result.token);
                localStorage.setItem('user', JSON.stringify(result.user));
                
                addLog('已保存认证信息到localStorage');
                checkAuth();
                
                // 获取完整用户资料
                await fetchUserProfile(result.token);
                
            } catch (error) {
                addLog('登录失败: ' + error.message);
            }
        }

        async function fetchUserProfile(token) {
            try {
                addLog('正在获取用户资料...');
                
                const response = await fetch('http://localhost:3000/api/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const profile = await response.json();
                addLog('获取用户资料成功');
                
                // 更新localStorage中的用户信息
                localStorage.setItem('user', JSON.stringify(profile));
                
                addLog(`用户姓名: ${profile.name || '未设置'}`);
                addLog(`用户邮箱: ${profile.email || '未设置'}`);
                addLog(`用户头像: ${profile.avatar ? '已设置' : '未设置'}`);
                
                checkAuth();
                
            } catch (error) {
                addLog('获取用户资料失败: ' + error.message);
            }
        }

        function clearStorage() {
            localStorage.clear();
            addLog('已清除所有存储');
            checkAuth();
        }

        // 页面加载时检查状态
        window.onload = function() {
            addLog('页面已加载');
            checkAuth();
        };
    </script>
</body>
</html>
