<template>
  <div class="page-container">
    <NavBar title="个人资料" left-arrow @click-left="$router.back()" />
    
    <div class="content-container">
      <!-- 用户头像和基本信息 -->
      <div class="profile-header">
        <div class="avatar-section">
          <div class="avatar-container">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" class="avatar-img" />
            <Icon v-else name="user-o" size="40" color="#999" />
          </div>
          <div class="upload-hint">点击更换头像</div>
        </div>
        
        <div class="user-basic-info">
          <div class="user-name">{{ userName }}</div>
          <div class="user-role">{{ userRole }}</div>
        </div>
      </div>
      
      <!-- 用户详细信息 -->
      <div class="profile-content">
        <Cell.Group>
          <Cell title="用户名" :value="userInfo.username || '-'" />
          <Cell title="姓名" :value="userInfo.name || userInfo.real_name || '-'" />
          <Cell title="邮箱" :value="userInfo.email || '-'" />
          <Cell title="手机号" :value="userInfo.phone || '-'" />
          <Cell title="部门" :value="userInfo.department_name || '-'" />
          <Cell title="角色" :value="userRole" />
          <Cell title="状态" :value="userInfo.status === 1 ? '正常' : '禁用'" />
          <Cell title="注册时间" :value="formatDate(userInfo.created_at)" />
        </Cell.Group>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <Button type="primary" block @click="handleEditProfile">编辑资料</Button>
        <Button type="default" block @click="handleChangePassword" style="margin-top: 12px;">修改密码</Button>
        <Button type="danger" block @click="handleLogout" style="margin-top: 12px;">退出登录</Button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { NavBar, Icon, Cell, Button, showConfirmDialog, showToast } from 'vant';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// 计算用户信息
const userInfo = computed(() => {
  return authStore.user || {};
});

const userName = computed(() => {
  return userInfo.value.name || userInfo.value.real_name || userInfo.value.username || '用户';
});

const userRole = computed(() => {
  if (userInfo.value.role === 'admin') return '管理员';
  if (userInfo.value.role === 'manager') return '经理';
  if (userInfo.value.role === 'user') return '用户';
  return userInfo.value.role || '用户';
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 编辑资料
const handleEditProfile = () => {
  showToast('编辑功能开发中');
};

// 修改密码
const handleChangePassword = () => {
  showToast('修改密码功能开发中');
};

// 退出登录
const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？',
    });
    
    authStore.logout();
    router.replace('/login');
    showToast('已退出登录');
  } catch (error) {
    // 用户取消
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: $background-color;
}

.content-container {
  padding-top: 46px;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  text-align: center;
  color: white;
  
  .avatar-section {
    margin-bottom: 20px;
    
    .avatar-container {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 8px;
      overflow: hidden;
      
      .avatar-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 40px;
      }
    }
    
    .upload-hint {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .user-basic-info {
    .user-name {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .user-role {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.profile-content {
  margin: 16px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  padding: 20px 16px;
}

:deep(.van-cell) {
  padding: 16px;
  
  .van-cell__title {
    color: $text-color;
    font-weight: 500;
  }
  
  .van-cell__value {
    color: $text-color-secondary;
  }
}

:deep(.van-button) {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
}
</style>
