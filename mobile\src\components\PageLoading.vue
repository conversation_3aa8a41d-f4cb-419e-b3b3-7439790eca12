<template>
  <div class="page-loading" v-if="visible">
    <div class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">{{ text }}</div>
        <div class="loading-progress" v-if="showProgress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <div class="progress-text">{{ progress }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  text: {
    type: String,
    default: '加载中...'
  },
  showProgress: {
    type: <PERSON>olean,
    default: false
  },
  progress: {
    type: Number,
    default: 0
  }
});

// 监听visible变化，添加/移除body滚动锁定
watch(() => props.visible, (newVal) => {
  if (newVal) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
});
</script>

<style lang="scss" scoped>
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-content {
  text-align: center;
  padding: 32px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 16px;

  .spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: $primary-color;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: -0.4s;
      width: 80%;
      height: 80%;
      top: 10%;
      left: 10%;
      border-top-color: rgba($primary-color, 0.6);
    }

    &:nth-child(3) {
      animation-delay: -0.8s;
      width: 60%;
      height: 60%;
      top: 20%;
      left: 20%;
      border-top-color: rgba($primary-color, 0.3);
    }
  }
}

.loading-text {
  font-size: 14px;
  color: $text-color;
  margin-bottom: 16px;
}

.loading-progress {
  .progress-bar {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, $primary-color, lighten($primary-color, 20%));
      border-radius: 2px;
      transition: width 0.3s ease;
    }
  }

  .progress-text {
    font-size: 12px;
    color: $text-color-secondary;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
