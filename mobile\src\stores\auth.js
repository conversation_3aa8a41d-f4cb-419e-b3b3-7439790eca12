import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))
  
  const isAuthenticated = computed(() => !!token.value)
  
  // 设置请求头中的token
  const setAuthHeader = () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      localStorage.setItem('token', token.value)
    } else {
      delete api.defaults.headers.common['Authorization']
      localStorage.removeItem('token')
    }
  }
  
  // 初始化设置
  setAuthHeader()
  
  // 登录
  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials)
      token.value = response.data.token
      user.value = response.data.user
      
      localStorage.setItem('token', token.value)
      localStorage.setItem('user', JSON.stringify(user.value))
      
      setAuthHeader()
      
      return true
    } catch (error) {
      console.error('登录失败:', error)
      return false
    }
  }
  
  // 登出
  const logout = () => {
    token.value = ''
    user.value = null
    
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    setAuthHeader()
  }
  
  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      console.log('正在获取用户资料...')
      const response = await api.get('/auth/profile')
      console.log('获取到的用户资料:', response.data)

      // 特别检查头像数据
      if (response.data.avatar) {
        console.log('✅ API返回了头像数据，长度:', response.data.avatar.length)
        console.log('头像前缀:', response.data.avatar.substring(0, 50))
      } else {
        console.log('❌ API没有返回头像数据')
      }

      user.value = response.data
      localStorage.setItem('user', JSON.stringify(user.value))
      console.log('用户资料已保存到localStorage')

      // 验证保存的数据
      const savedUser = JSON.parse(localStorage.getItem('user'))
      if (savedUser.avatar) {
        console.log('✅ localStorage中保存了头像数据')
      } else {
        console.log('❌ localStorage中没有头像数据')
      }

      return true
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return false
    }
  }
  
  return {
    token,
    user,
    isAuthenticated,
    login,
    logout,
    fetchUserProfile
  }
}) 