<template>
  <div class="mobile-form">
    <Form ref="formRef" :model="formData" :rules="formRules">
      <div v-for="(field, index) in fields" :key="field.name || index" class="form-section">
        <!-- 分组标题 -->
        <div v-if="field.type === 'group'" class="form-group-title">
          {{ field.title }}
        </div>
        
        <!-- 普通字段 -->
        <template v-else>
          <!-- 输入框 -->
          <Field
            v-if="field.type === 'input'"
            v-model="formData[field.name]"
            :name="field.name"
            :label="field.label"
            :placeholder="field.placeholder"
            :type="field.inputType || 'text'"
            :readonly="field.readonly"
            :disabled="field.disabled"
            :required="field.required"
            :rules="field.rules"
            :maxlength="field.maxlength"
            @click="handleFieldClick(field)"
          >
            <template #left-icon v-if="field.leftIcon">
              <Icon :name="field.leftIcon" />
            </template>
            <template #right-icon v-if="field.rightIcon">
              <Icon :name="field.rightIcon" />
            </template>
          </Field>
          
          <!-- 文本域 -->
          <Field
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.name]"
            :name="field.name"
            :label="field.label"
            :placeholder="field.placeholder"
            type="textarea"
            :rows="field.rows || 3"
            :readonly="field.readonly"
            :disabled="field.disabled"
            :required="field.required"
            :rules="field.rules"
            :maxlength="field.maxlength"
            autosize
          />
          
          <!-- 选择器 -->
          <Field
            v-else-if="field.type === 'select'"
            v-model="formData[field.name]"
            :name="field.name"
            :label="field.label"
            :placeholder="field.placeholder"
            readonly
            :required="field.required"
            :rules="field.rules"
            @click="handleSelectClick(field)"
          >
            <template #right-icon>
              <Icon name="arrow-down" />
            </template>
          </Field>
          
          <!-- 日期选择 -->
          <Field
            v-else-if="field.type === 'date'"
            v-model="formData[field.name]"
            :name="field.name"
            :label="field.label"
            :placeholder="field.placeholder"
            readonly
            :required="field.required"
            :rules="field.rules"
            @click="handleDateClick(field)"
          >
            <template #right-icon>
              <Icon name="calendar-o" />
            </template>
          </Field>
          
          <!-- 开关 -->
          <Cell
            v-else-if="field.type === 'switch'"
            :title="field.label"
            :required="field.required"
          >
            <template #right-icon>
              <Switch
                v-model="formData[field.name]"
                :disabled="field.disabled"
                @change="handleSwitchChange(field, $event)"
              />
            </template>
          </Cell>
          
          <!-- 单选框组 -->
          <div v-else-if="field.type === 'radio'" class="form-radio-group">
            <div class="form-field-label" :class="{ required: field.required }">
              {{ field.label }}
            </div>
            <RadioGroup v-model="formData[field.name]" direction="horizontal">
              <Radio
                v-for="option in field.options"
                :key="option.value"
                :name="option.value"
                :disabled="field.disabled"
              >
                {{ option.label }}
              </Radio>
            </RadioGroup>
          </div>
          
          <!-- 复选框组 -->
          <div v-else-if="field.type === 'checkbox'" class="form-checkbox-group">
            <div class="form-field-label" :class="{ required: field.required }">
              {{ field.label }}
            </div>
            <CheckboxGroup v-model="formData[field.name]">
              <Checkbox
                v-for="option in field.options"
                :key="option.value"
                :name="option.value"
                :disabled="field.disabled"
              >
                {{ option.label }}
              </Checkbox>
            </CheckboxGroup>
          </div>
          
          <!-- 文件上传 -->
          <div v-else-if="field.type === 'upload'" class="form-upload">
            <div class="form-field-label" :class="{ required: field.required }">
              {{ field.label }}
            </div>
            <Uploader
              v-model="formData[field.name]"
              :multiple="field.multiple"
              :max-count="field.maxCount"
              :accept="field.accept"
              :disabled="field.disabled"
              @oversize="handleUploadOversize"
              @delete="handleUploadDelete"
            />
          </div>
        </template>
      </div>
    </Form>
    
    <!-- 选择器弹窗 -->
    <Popup v-model:show="showPicker" position="bottom">
      <Picker
        :columns="pickerColumns"
        @confirm="handlePickerConfirm"
        @cancel="showPicker = false"
      />
    </Popup>
    
    <!-- 日期选择弹窗 -->
    <Popup v-model:show="showDatePicker" position="bottom">
      <DatePicker
        v-model="selectedDate"
        :type="datePickerType"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </Popup>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { 
  Form, Field, Cell, Switch, RadioGroup, Radio, CheckboxGroup, Checkbox, 
  Uploader, Popup, Picker, DatePicker, Icon, showToast 
} from 'vant';

const props = defineProps({
  fields: {
    type: Array,
    required: true
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  rules: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'field-click', 'submit', 'change']);

const formRef = ref(null);
const formData = reactive({ ...props.modelValue });
const formRules = reactive({ ...props.rules });

// 选择器相关
const showPicker = ref(false);
const pickerColumns = ref([]);
const currentPickerField = ref(null);

// 日期选择器相关
const showDatePicker = ref(false);
const selectedDate = ref(new Date());
const datePickerType = ref('date');
const currentDateField = ref(null);

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', newVal);
  emit('change', newVal);
}, { deep: true });

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(formData, newVal);
}, { deep: true });

// 处理字段点击
const handleFieldClick = (field) => {
  emit('field-click', field);
};

// 处理选择器点击
const handleSelectClick = (field) => {
  currentPickerField.value = field;
  pickerColumns.value = field.options || [];
  showPicker.value = true;
};

// 处理选择器确认
const handlePickerConfirm = ({ selectedValues, selectedOptions }) => {
  if (currentPickerField.value) {
    formData[currentPickerField.value.name] = selectedValues[0];
  }
  showPicker.value = false;
};

// 处理日期选择点击
const handleDateClick = (field) => {
  currentDateField.value = field;
  datePickerType.value = field.dateType || 'date';
  selectedDate.value = formData[field.name] ? new Date(formData[field.name]) : new Date();
  showDatePicker.value = true;
};

// 处理日期确认
const handleDateConfirm = (value) => {
  if (currentDateField.value) {
    formData[currentDateField.value.name] = value;
  }
  showDatePicker.value = false;
};

// 处理开关变化
const handleSwitchChange = (field, value) => {
  if (field.onChange) {
    field.onChange(value);
  }
};

// 处理文件上传超出大小
const handleUploadOversize = () => {
  showToast('文件大小超出限制');
};

// 处理文件删除
const handleUploadDelete = (file, detail) => {
  console.log('文件删除:', file, detail);
};

// 表单验证
const validate = () => {
  return formRef.value?.validate();
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetValidation();
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 获取表单数据
const getFormData = () => {
  return { ...formData };
};

defineExpose({
  validate,
  resetForm,
  getFormData
});
</script>

<style lang="scss" scoped>
.mobile-form {
  background-color: #f8f9fa;
}

.form-section {
  margin-bottom: 8px;
  background-color: #fff;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-group-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  color: $text-color;
  background-color: #f8f9fa;
  border-bottom: 1px solid $border-color;
}

.form-field-label {
  padding: 16px 16px 8px;
  font-size: 14px;
  color: $text-color;
  
  &.required::before {
    content: '*';
    color: #ff4757;
    margin-right: 4px;
  }
}

.form-radio-group,
.form-checkbox-group {
  padding: 0 16px 16px;
}

.form-upload {
  padding: 0 16px 16px;
}

// 自定义表单样式
:deep(.van-field__label) {
  color: $text-color;
  font-weight: 500;
}

:deep(.van-field__control) {
  color: $text-color;
}

:deep(.van-field--required .van-field__label::before) {
  color: #ff4757;
}

:deep(.van-radio-group--horizontal) {
  flex-wrap: wrap;
  gap: 16px;
}

:deep(.van-checkbox-group) {
  flex-wrap: wrap;
  gap: 16px;
}
</style>
